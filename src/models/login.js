import { message } from "antd";
import { routerRedux } from "dva/router";
import { userAPI } from "@/services";
import { cloneDeep } from "lodash";
import { compare, multipleToNull } from "@/utils/modelOperator";
import { rsaPwd } from "@/utils/user";
import { clearStorage } from "@/utils";
export default {
	namespace: "login",

	state: {
		status: null,
		submitting: false,
		changePasswordShow: false,
		userInfo: null,
		loginData: {
			account: null,
			password: null
		},
		modifyPasswordData: {
			oldPwd: null,
			newPwd: null,
			confirmNewPwd: null
		}
	},

	effects: {
		// 模拟登录
		*mockLogin({payload}, {call}) {
			const { account, password } = payload;
			const params = { account, password: rsaPwd(password) };
			let {tempRandom, authMessage} = ["", ""];
			// 获取加盐随机数
			const authResult = yield call(userAPI.auth, params);

			tempRandom = authResult.data;
			authMessage = authResult.message;

			if (tempRandom) {
				const res = yield call(userAPI.userLogin, {...params, tempRandom});
				if (res.success) {
					const csrfToken = res.data.csrfToken;
					sessionStorage.setItem("_csrf_", csrfToken);
					localStorage.setItem("_sync_qjt_csrf_", csrfToken); // 新的csrf同步到其他页面
					localStorage.setItem("developmentLoginData", JSON.stringify(params));

					// 显示登录成功消息，然后延迟刷新页面
					message.success(res.message || "登录成功！", 1.5);
					setTimeout(() => {
						location.reload();
					}, 1600);
				} else {
					message.error(res.message);
					return Promise.reject(res.message);
				}
				return;
			}
			message.error(authMessage);
			return Promise.reject(authMessage);
		},

		// 只调用登出接口
		*signOut({}, { call }) {
			if (process.env.SYS_ENV === "development") {
				return;
			}
			const response = yield call(userAPI.signOut);
			if (response) {
				sessionStorage.setItem("_csrf_", "");
				// sessionStorage.clear();
				// localStorage.clear();
				clearStorage();
			}
		},
		// 调用登出接口且跳转到登录页面
		*logout({}, { call, put }) {
			const response = yield call(userAPI.signOut);
			if (response) {
				yield put({
					type: "goLogin",
					payload: {
						homePage: true
					}
				});
			}
		},
		// 只跳转到登录页面
		*goLogin({payload}, { put }) {
			sessionStorage.setItem("_csrf_", "");
			// sessionStorage.clear();
			// localStorage.clear();
			clearStorage();
			const { homePage } = payload || {};
			const {origin, pathname, search} = window.location || {};
			if (pathname !== "/user/login") {
				if (homePage) {
					window.location.href = "/user/login";
					// yield put(routerRedux.push("/user/login"));
				} else {
					const callbackUrl = origin + pathname + encodeURIComponent(search);
					yield put(routerRedux.push("/user/login?callbackUrl=" + callbackUrl));
				}
			}
		}
	},

	reducers: {
		setAttrValue(state, { payload }) {
			return (function multiple(state, newState) {
				let stateChange = state;
				// 用于在不按照state模板的情况下,payload添加属性和属性值的情况下使用
				stateChange = compare(stateChange, newState);
				for (let [key, value] of Object.entries(stateChange)) {
					// 这里严格判断value是否是对象{},不能使用typeof,原因自己查
					if (Object.prototype.toString.call(value) === "[object Object]" && newState[key] !== undefined && newState[key] !== null) {
						stateChange[key] = multiple(value, newState[key]);
					} else {
						if (newState[key] !== undefined && newState[key] !== null) {
							stateChange[key] = newState[key];
						}
						if (newState[key] === null) {
							stateChange[key] = multipleToNull(stateChange[key]);
						}
					}
				}
				return stateChange;
			})(cloneDeep(state), payload);
		}
	}
};
